import time
import os
import sys
import math
from media.sensor import *
from media.display import *
from media.media import *
from time import ticks_ms
from machine import FPIOA, Pin, TOUCH
from stepper_motor import x_controller, y_controller

# 初始化FPIOA并创建Pin对象
fpioa = FPIOA()

# 激光笔控制引脚 GPIO26
fpioa.set_function(26, FPIOA.GPIO26)
laser_pin = Pin(26, Pin.OUT)
laser_pin.value(0)  # 初始关闭激光

# 调试开关
DEBUG = True
def log(msg):
    if DEBUG:
        print(f"[LASER] {msg}")

# 快速PID控制器 - 优化参数以提高响应速度
class FastPID:
    def __init__(self, kp=0.8, ki=0.1, kd=0.3, max_output=50):
        self.kp = kp
        self.ki = ki
        self.kd = kd
        self.max_output = max_output
        self.last_error = 0
        self.integral = 0
        self.integral_limit = 20

    def compute(self, error, dt):
        # 积分项限幅
        self.integral += error * dt
        self.integral = max(min(self.integral, self.integral_limit), -self.integral_limit)

        # 微分项
        derivative = (error - self.last_error) / dt if dt > 0 else 0

        # PID输出
        output = self.kp * error + self.ki * self.integral + self.kd * derivative

        # 输出限幅
        output = max(min(output, self.max_output), -self.max_output)

        self.last_error = error
        return output

# 创建PID控制器
pid_x = FastPID(kp=1.2, ki=0.05, kd=0.4, max_output=80)
pid_y = FastPID(kp=1.2, ki=0.05, kd=0.4, max_output=80)

# 颜色阈值 - 优化以提高检测速度和准确性
BLACK_THRESHOLD = (0, 40, -25, 25, -25, 25)  # 黑色边框
WHITE_THRESHOLD = (55, 100, -20, 20, -20, 20)  # 白色内部区域
RED_THRESHOLD = (30, 100, 15, 127, 15, 127)   # 红色靶心

# 快速矩形检测函数
def find_target_fast(img):
    """快速目标检测 - 优先速度"""
    # 使用较低的阈值以提高检测速度
    black_blobs = img.find_blobs([BLACK_THRESHOLD],
                                pixels_threshold=300,
                                area_threshold=3000,
                                merge=True,
                                x_stride=2,  # 跳跃采样提高速度
                                y_stride=2)

    if not black_blobs:
        return None, None

    # 选择最大的黑色区域
    largest_black = max(black_blobs, key=lambda b: b.area())

    # 快速验证是否为矩形框
    area = largest_black.w() * largest_black.h()
    pixel_density = largest_black.pixels() / area if area > 0 else 0

    # 如果是空心矩形框（密度较低）
    if pixel_density < 0.4:
        # 在黑框内寻找白色区域或红色靶心
        roi = largest_black.rect()

        # 首先尝试找红色靶心（更精确）
        red_blobs = img.find_blobs([RED_THRESHOLD],
                                  roi=roi,
                                  pixels_threshold=5,
                                  area_threshold=10,
                                  merge=True)

        if red_blobs:
            largest_red = max(red_blobs, key=lambda b: b.area())
            return largest_black, largest_red

        # 如果没找到红色，寻找白色区域中心
        white_blobs = img.find_blobs([WHITE_THRESHOLD],
                                   roi=roi,
                                   pixels_threshold=50,
                                   area_threshold=100,
                                   merge=True)

        if white_blobs:
            largest_white = max(white_blobs, key=lambda b: b.area())
            # 验证白色区域是否在黑框中心附近
            center_distance = math.sqrt(
                (largest_white.cx() - largest_black.cx()) ** 2 +
                (largest_white.cy() - largest_black.cy()) ** 2
            )

            if center_distance < 30:  # 放宽距离要求以提高检测率
                return largest_black, largest_white

    return largest_black, None

# 快速中心点计算
def calculate_target_center(frame_blob, inner_blob=None):
    """计算目标中心点"""
    if inner_blob:
        # 如果有内部区域（红色靶心或白色区域），使用其中心
        return inner_blob.cx(), inner_blob.cy()
    else:
        # 否则使用矩形框的几何中心
        return frame_blob.cx(), frame_blob.cy()

# 步进电机控制函数
def move_to_target(x_error, y_error, dt):
    """根据误差控制步进电机移动"""
    # 计算PID输出
    x_output = pid_x.compute(x_error, dt)
    y_output = pid_y.compute(y_error, dt)

    # 转换为步数（根据实际标定调整比例）
    x_steps = int(abs(x_output) * 0.5)  # 调整比例系数
    y_steps = int(abs(y_output) * 0.5)

    # 确定方向
    x_direction = 1 if x_output > 0 else -1
    y_direction = 1 if y_output > 0 else -1

    # 执行移动（使用较快的延时）
    if x_steps > 0:
        x_controller.move_steps(x_steps, x_direction, delay_ms=2)
    if y_steps > 0:
        y_controller.move_steps(y_steps, y_direction, delay_ms=2)

    return x_steps, y_steps

sensor = None
try:
    log("初始化激光瞄准系统...")

    # 使用较低分辨率以提高处理速度
    sensor = Sensor(width=640, height=480)
    sensor.reset()
    sensor.set_framesize(width=640, height=480)
    sensor.set_pixformat(Sensor.RGB565)

    Display.init(Display.ST7701, to_ide=True, width=800, height=480)
    MediaManager.init()
    sensor.run()

    # 图像中心点
    CENTER_X = 320
    CENTER_Y = 240

    # 时间控制变量
    start_time = ticks_ms()
    target_found_time = None
    last_time = ticks_ms()

    # 状态变量
    target_found = False
    target_locked = False
    firing_complete = False

    clock = time.clock()

    log("开始目标搜索...")

    while True:
        clock.tick()
        current_time = ticks_ms()
        dt = (current_time - last_time) / 1000.0
        last_time = current_time

        # 检查是否超时（2秒）
        elapsed_time = (current_time - start_time) / 1000.0
        if elapsed_time > 2.0 and not firing_complete:
            log("超时！强制发射激光")
            laser_pin.value(1)
            time.sleep_ms(100)
            laser_pin.value(0)
            firing_complete = True
            break

        img = sensor.snapshot()

        # 快速目标检测
        frame_blob, inner_blob = find_target_fast(img)

        if frame_blob:
            if not target_found:
                target_found = True
                target_found_time = elapsed_time
                log(f"目标发现于 {target_found_time:.3f}s")

            # 计算目标中心
            target_x, target_y = calculate_target_center(frame_blob, inner_blob)

            # 计算误差
            x_error = target_x - CENTER_X
            y_error = target_y - CENTER_Y

            # 计算距离误差
            distance_error = math.sqrt(x_error**2 + y_error**2)

            # 绘制检测结果
            img.draw_rectangle(frame_blob.rect(), color=(255, 255, 255), thickness=2)
            img.draw_cross(target_x, target_y, color=(255, 0, 0), size=8)
            img.draw_cross(CENTER_X, CENTER_Y, color=(0, 255, 0), size=6)

            if inner_blob:
                img.draw_rectangle(inner_blob.rect(), color=(0, 255, 255), thickness=1)

            # 显示状态信息
            img.draw_string(10, 10, f"Time: {elapsed_time:.2f}s", color=(255, 255, 0), scale=2)
            img.draw_string(10, 30, f"Error: {distance_error:.1f}px", color=(255, 255, 0), scale=2)
            img.draw_string(10, 50, f"Target: ({target_x}, {target_y})", color=(255, 0, 0), scale=1)

            # 判断是否足够接近目标
            if distance_error < 15:  # 放宽精度要求以优先时间
                if not target_locked:
                    target_locked = True
                    log(f"目标锁定于 {elapsed_time:.3f}s，距离误差: {distance_error:.1f}px")

                # 发射激光
                log("发射激光！")
                laser_pin.value(1)
                time.sleep_ms(200)  # 激光持续时间
                laser_pin.value(0)

                completion_time = (ticks_ms() - start_time) / 1000.0
                log(f"任务完成！总用时: {completion_time:.3f}s")
                firing_complete = True

                img.draw_string(10, 70, "LASER FIRED!", color=(0, 255, 0), scale=3)
                break
            else:
                # 控制步进电机移动
                x_steps, y_steps = move_to_target(x_error, y_error, dt)

                if x_steps > 0 or y_steps > 0:
                    log(f"移动: X={x_steps}步, Y={y_steps}步")
        else:
            # 未找到目标
            img.draw_string(10, 10, f"Time: {elapsed_time:.2f}s", color=(255, 255, 0), scale=2)
            img.draw_string(10, 30, "Searching target...", color=(255, 0, 0), scale=2)

        # 显示FPS
        img.draw_string(10, 200, f"FPS: {clock.fps():.1f}", color=(0, 255, 0), scale=1)

        Display.show_image(img)

        # 避免过度占用CPU
        time.sleep_ms(5)

except KeyboardInterrupt as e:
    print("用户停止: ", e)
except BaseException as e:
    print(f"异常: {e}")
finally:
    # 确保激光关闭
    laser_pin.value(0)
    # 停止电机
    x_controller.stop()
    y_controller.stop()

    if isinstance(sensor, Sensor):
        sensor.stop()
    Display.deinit()
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    MediaManager.deinit()

    log("系统安全关闭")
