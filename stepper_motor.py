# K230二维步进电机控制 - 基于四相步进电机控制
import time
import os
import sys
from machine import FPIOA, Pin, Timer

# 调试信息
DEBUG = True
def log(msg):
    if DEBUG:
        print(f"[DEBUG] {msg}")

# 步进序列
STEP_SEQUENCE = [[1, 1, 0, 0], [0, 1, 1, 0], [0, 0, 1, 1], [1, 0, 0, 1]]

# GPIO引脚定义
# X轴：GPIO49, 48, 2, 3
X_PINS = [49, 48, 2, 3]
# Y轴：GPIO35,42,52,37
Y_PINS = [35,42,52,37]

# 初始化FPIOA
fpioa = FPIOA()

# 初始化X轴引脚
for pin_num in X_PINS:
    fpioa.set_function(pin_num, getattr(FPIOA, f"GPIO{pin_num}"))

# 初始化Y轴引脚
for pin_num in Y_PINS:
    fpioa.set_function(pin_num, getattr(FPIOA, f"GPIO{pin_num}"))

# 创建引脚对象
x_pins = [Pin(pin, Pin.OUT) for pin in X_PINS]
y_pins = [Pin(pin, Pin.OUT) for pin in Y_PINS]

# 初始化所有引脚为低电平
for pin in x_pins + y_pins:
    pin.value(0)

log("GPIO初始化完成")

# 步进电机控制类
class StepperController:
    def __init__(self, pins, name):
        self.pins = pins
        self.name = name
        self.step_index = 0
        self.direction = 1

    def set_step(self, step_index):
        """设置步进电机的当前步"""
        step_data = STEP_SEQUENCE[step_index % 4]
        for pin, value in zip(self.pins, step_data):
            pin.value(value)

    def move_steps(self, steps, direction=1, delay_ms=5):
        """移动指定步数"""
        log(f"{self.name}移动: 步数={steps}, 方向={'正' if direction==1 else '负'}")

        for i in range(steps):
            if direction == 1:
                self.step_index = (self.step_index + 1) % 4
            else:
                self.step_index = (self.step_index - 1 + 4) % 4

            self.set_step(self.step_index)
            time.sleep_ms(delay_ms)

            if i % 10 == 0:
                log(f"{self.name}进度: {i+1}/{steps}")

        log(f"{self.name}完成: 步数={steps}")

    def stop(self):
        """停止电机"""
        for pin in self.pins:
            pin.value(0)

# 创建控制器实例
x_controller = StepperController(x_pins, "X轴")
y_controller = StepperController(y_pins, "Y轴")

# 测试函数
def test_motors():
    """测试两个步进电机"""
    log("开始步进电机测试...")

    # 测试X轴
    log("测试X轴正转100步...")
    x_controller.move_steps(100, direction=1, delay_ms=3)
    time.sleep_ms(2000)

    log("测试X轴反转100步...")
    x_controller.move_steps(100, direction=-1, delay_ms=3)
    time.sleep_ms(2000)

    # 测试Y轴
    log("测试Y轴正转100步...")
    y_controller.move_steps(100, direction=1, delay_ms=3)
    time.sleep_ms(1000)

    log("测试Y轴反转100步...")
    y_controller.move_steps(100, direction=-1, delay_ms=3)

    # 停止电机
    x_controller.stop()
    y_controller.stop()

    log("测试完成")

# 单独控制函数
def move_x(steps, direction=1, delay_ms=3):
    """控制X轴步进电机"""
    x_controller.move_steps(steps, direction, delay_ms)

def move_y(steps, direction=1, delay_ms=3):
    """控制Y轴步进电机"""
    y_controller.move_steps(steps, direction, delay_ms)

# 直接运行测试
if __name__ == "__main__":
    test_motors()
